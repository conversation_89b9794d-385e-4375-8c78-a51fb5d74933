# Piper TTS API

A standalone FastAPI server for Piper Text-to-Speech using ONNX models.

## Features

- FastAPI-based REST API
- Support for Nepali TTS using Piper ONNX models
- Speaker selection and speech rate control
- Simple setup with minimal dependencies

## Setup

1. Install dependencies:
```bash
uv sync
```

2. Activate the virtual environment:
```bash
source .venv/bin/activate
```

## Usage

### Start the API Server

```bash
python run_piper_api.py
```

The API will be available at:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs

### Test the API

```bash
python test_piper_tts.py
```

## API Endpoints

### Health Check
```
GET /
```

### Voice Information
```
GET /voice/info
```

### Available Speakers
```
GET /speakers
```

### Text-to-Speech Synthesis
```
GET /synthesize?text=नमस्ते&speaker_id=0&speech_rate=1.0
POST /synthesize
```

Parameters:
- `text`: Text to synthesize (required)
- `speaker_id`: Speaker ID (optional, 0-based)
- `speech_rate`: Speech rate (optional, 0.5-3.0, default: 1.0)
- `noise_scale`: Generator noise (optional, 0.0-1.0)
- `noise_w`: Phoneme width noise (optional, 0.0-1.0)
- `sentence_silence`: Silence after sentences (optional, 0.0-2.0 seconds)

## Models

The API automatically loads the Nepali model from:
- `models/ne_NP-google-medium.onnx`
- `models/ne_NP-google-medium.onnx.json`

## Dependencies

- FastAPI with uvicorn
- ONNX Runtime
- piper-phonemize
- NumPy
- Requests (for testing)