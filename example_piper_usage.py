#!/usr/bin/env python3
"""
Comprehensive usage examples for the new Piper TTS API.
"""

import requests
import urllib.parse
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_health_and_info():
    """Test health check and voice information."""
    print("=== Health Check & Voice Info ===")
    
    # Health check
    response = requests.get(f"{BASE_URL}/")
    print(f"Health: {response.json()}")
    
    # Voice information
    response = requests.get(f"{BASE_URL}/voice/info")
    info = response.json()
    print(f"Voice Info:")
    print(f"  - Speakers: {info['num_speakers']}")
    print(f"  - Sample Rate: {info['sample_rate']} Hz")
    print(f"  - Language: {info['espeak_voice']}")
    print(f"  - Phoneme Type: {info['phoneme_type']}")
    
    # Speaker information
    response = requests.get(f"{BASE_URL}/speakers")
    speakers = response.json()
    print(f"  - Available Speakers: 0-{speakers['num_speakers']-1}")

def test_basic_synthesis():
    """Test basic text-to-speech synthesis."""
    print("\n=== Basic Synthesis ===")
    
    # Simple Nepali text
    text = "नमस्ते, म पाइपर हुँ।"  # "Hello, I am Piper."
    
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": text,
        "speaker_id": 0,
        "speech_rate": 1.0
    })
    
    if response.status_code == 200:
        filename = "basic_synthesis.wav"
        with open(filename, "wb") as f:
            f.write(response.content)
        print(f"✓ Basic synthesis saved to {filename}")
        print(f"  Audio size: {len(response.content)} bytes")
    else:
        print(f"✗ Basic synthesis failed: {response.status_code}")

def test_speaker_variations():
    """Test different speakers."""
    print("\n=== Speaker Variations ===")
    
    text = "विभिन्न आवाजहरू"  # "Different voices"
    speakers_to_test = [0, 3, 7, 12, 17]
    
    for speaker_id in speakers_to_test:
        response = requests.get(f"{BASE_URL}/synthesize", params={
            "text": text,
            "speaker_id": speaker_id,
            "speech_rate": 1.0
        })
        
        if response.status_code == 200:
            filename = f"speaker_{speaker_id}.wav"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"✓ Speaker {speaker_id}: {len(response.content)} bytes -> {filename}")
        else:
            print(f"✗ Speaker {speaker_id} failed: {response.status_code}")

def test_speech_rates():
    """Test different speech rates."""
    print("\n=== Speech Rate Control ===")
    
    text = "गति नियन्त्रण परीक्षण"  # "Speed control test"
    rates = [0.5, 0.8, 1.0, 1.3, 1.8]
    
    for rate in rates:
        response = requests.get(f"{BASE_URL}/synthesize", params={
            "text": text,
            "speaker_id": 0,
            "speech_rate": rate
        })
        
        if response.status_code == 200:
            filename = f"rate_{rate}.wav"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"✓ Rate {rate}x: {len(response.content)} bytes -> {filename}")
        else:
            print(f"✗ Rate {rate}x failed: {response.status_code}")

def test_advanced_parameters():
    """Test advanced synthesis parameters."""
    print("\n=== Advanced Parameters ===")
    
    text = "उन्नत सेटिङहरू"  # "Advanced settings"
    
    # Test with different noise parameters
    configs = [
        {"noise_scale": 0.2, "noise_w": 0.5, "name": "low_variability"},
        {"noise_scale": 0.667, "noise_w": 0.8, "name": "default"},
        {"noise_scale": 1.0, "noise_w": 1.0, "name": "high_variability"},
    ]
    
    for config in configs:
        response = requests.get(f"{BASE_URL}/synthesize", params={
            "text": text,
            "speaker_id": 5,
            "speech_rate": 1.0,
            "noise_scale": config["noise_scale"],
            "noise_w": config["noise_w"]
        })
        
        if response.status_code == 200:
            filename = f"advanced_{config['name']}.wav"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"✓ {config['name']}: {len(response.content)} bytes -> {filename}")
        else:
            print(f"✗ {config['name']} failed: {response.status_code}")

def test_post_requests():
    """Test POST requests with JSON."""
    print("\n=== POST Requests ===")
    
    # Complex synthesis with all parameters
    data = {
        "text": "यो जटिल संश्लेषण अनुरोध हो जसमा सबै प्यारामिटरहरू छन्।",
        "speaker_id": 8,
        "speech_rate": 1.1,
        "noise_scale": 0.5,
        "noise_w": 0.7,
        "sentence_silence": 0.3
    }
    
    response = requests.post(f"{BASE_URL}/synthesize", json=data)
    
    if response.status_code == 200:
        filename = "post_complex.wav"
        with open(filename, "wb") as f:
            f.write(response.content)
        print(f"✓ Complex POST: {len(response.content)} bytes -> {filename}")
    else:
        print(f"✗ Complex POST failed: {response.status_code}")

def test_long_text():
    """Test synthesis with longer text."""
    print("\n=== Long Text Synthesis ===")
    
    long_text = """
    नेपाल दक्षिण एशियामा अवस्थित एक भूपरिवेष्ठित देश हो। 
    यो उत्तरमा चीन र दक्षिण, पूर्व र पश्चिममा भारतले घेरिएको छ। 
    नेपालको राजधानी काठमाडौं हो र यहाँका मानिसहरूले नेपाली भाषा बोल्छन्।
    """
    
    response = requests.post(f"{BASE_URL}/synthesize", json={
        "text": long_text.strip(),
        "speaker_id": 10,
        "speech_rate": 0.9,
        "sentence_silence": 0.5
    })
    
    if response.status_code == 200:
        filename = "long_text.wav"
        with open(filename, "wb") as f:
            f.write(response.content)
        print(f"✓ Long text: {len(response.content)} bytes -> {filename}")
        print(f"  Text length: {len(long_text.strip())} characters")
    else:
        print(f"✗ Long text failed: {response.status_code}")

def test_error_handling():
    """Test error handling."""
    print("\n=== Error Handling ===")
    
    # Test invalid speaker ID
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": "परीक्षण",
        "speaker_id": 999
    })
    print(f"Invalid speaker (999): {response.status_code}")
    
    # Test invalid speech rate
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": "परीक्षण",
        "speech_rate": 0.05  # Too slow
    })
    print(f"Invalid speech rate (0.05): {response.status_code}")
    
    # Test empty text
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": ""
    })
    print(f"Empty text: {response.status_code}")

def main():
    """Run all examples."""
    print("Piper TTS API - Comprehensive Usage Examples")
    print("=" * 60)
    
    try:
        # Check if server is running
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running. Please start it with:")
            print("   python run_piper_api.py --model models/ne_NP-google-medium.onnx")
            return
        
        test_health_and_info()
        test_basic_synthesis()
        test_speaker_variations()
        test_speech_rates()
        test_advanced_parameters()
        test_post_requests()
        test_long_text()
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 All examples completed successfully!")
        print("\nGenerated audio files:")
        for wav_file in sorted(Path(".").glob("*.wav")):
            if wav_file.name.startswith(("basic_", "speaker_", "rate_", "advanced_", "post_", "long_")):
                print(f"  - {wav_file}")
        
        print(f"\n📖 API Documentation: {BASE_URL}/docs")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start it with:")
        print("   python run_piper_api.py --model models/ne_NP-google-medium.onnx")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
