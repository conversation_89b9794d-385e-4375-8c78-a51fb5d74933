#!/usr/bin/env python3
"""
Test script for the Piper TTS API endpoints.
"""

import requests
import json
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint."""
    print("Testing health check endpoint...")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200
    print("✓ Health check passed\n")

def test_model_info():
    """Test the model info endpoint."""
    print("Testing model info endpoint...")
    response = requests.get(f"{BASE_URL}/model/info")
    print(f"Status: {response.status_code}")
    data = response.json()
    print(f"Model info: {json.dumps(data, indent=2)}")
    assert response.status_code == 200
    assert "language" in data
    assert "num_speakers" in data
    print("✓ Model info test passed\n")

def test_speakers():
    """Test the speakers endpoint."""
    print("Testing speakers endpoint...")
    response = requests.get(f"{BASE_URL}/speakers")
    print(f"Status: {response.status_code}")
    data = response.json()
    print(f"Speakers: {json.dumps(data, indent=2)}")
    assert response.status_code == 200
    assert "num_speakers" in data
    assert "speakers" in data
    print("✓ Speakers test passed\n")

def test_synthesis_post():
    """Test speech synthesis using POST."""
    print("Testing speech synthesis (POST)...")
    
    # Test data
    test_data = {
        "text": "नमस्ते, यो एक परीक्षण हो।",  # "Hello, this is a test." in Nepali
        "speaker_id": 0,
        "speech_rate": 1.0,
        "variability": 0.667,
        "noise_w": 0.8
    }
    
    response = requests.post(
        f"{BASE_URL}/synthesize",
        json=test_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Content-Type: {response.headers.get('content-type')}")
    print(f"Content-Length: {len(response.content)} bytes")
    
    assert response.status_code == 200
    assert response.headers.get('content-type') == 'audio/wav'
    assert len(response.content) > 1000  # Should be a reasonable size
    
    # Save the audio file
    output_path = Path("test_api_output_post.wav")
    with open(output_path, "wb") as f:
        f.write(response.content)
    print(f"Audio saved to: {output_path}")
    print("✓ POST synthesis test passed\n")

def test_synthesis_get():
    """Test speech synthesis using GET."""
    print("Testing speech synthesis (GET)...")
    
    params = {
        "text": "यो GET अनुरोध परीक्षण हो।",  # "This is a GET request test." in Nepali
        "speaker_id": 1,
        "speech_rate": 1.2,  # Slightly faster
        "variability": 0.5,
        "noise_w": 0.7
    }
    
    response = requests.get(f"{BASE_URL}/synthesize", params=params)
    
    print(f"Status: {response.status_code}")
    print(f"Content-Type: {response.headers.get('content-type')}")
    print(f"Content-Length: {len(response.content)} bytes")
    
    assert response.status_code == 200
    assert response.headers.get('content-type') == 'audio/wav'
    assert len(response.content) > 1000
    
    # Save the audio file
    output_path = Path("test_api_output_get.wav")
    with open(output_path, "wb") as f:
        f.write(response.content)
    print(f"Audio saved to: {output_path}")
    print("✓ GET synthesis test passed\n")

def test_different_speakers():
    """Test synthesis with different speakers."""
    print("Testing different speakers...")
    
    test_text = "विभिन्न वक्ताहरूको परीक्षण।"  # "Testing different speakers." in Nepali
    
    for speaker_id in [0, 5, 10, 15]:  # Test a few different speakers
        print(f"Testing speaker {speaker_id}...")
        
        params = {
            "text": test_text,
            "speaker_id": speaker_id,
            "speech_rate": 1.0
        }
        
        response = requests.get(f"{BASE_URL}/synthesize", params=params)
        
        if response.status_code == 200:
            output_path = Path(f"test_speaker_{speaker_id}.wav")
            with open(output_path, "wb") as f:
                f.write(response.content)
            print(f"✓ Speaker {speaker_id} - Audio saved to: {output_path}")
        else:
            print(f"✗ Speaker {speaker_id} failed with status {response.status_code}")
    
    print("✓ Different speakers test completed\n")

def test_speech_rates():
    """Test synthesis with different speech rates."""
    print("Testing different speech rates...")
    
    test_text = "गति परीक्षण।"  # "Speed test." in Nepali
    rates = [0.5, 1.0, 1.5, 2.0]  # Slow to fast
    
    for rate in rates:
        print(f"Testing speech rate {rate}...")
        
        params = {
            "text": test_text,
            "speaker_id": 0,
            "speech_rate": rate
        }
        
        response = requests.get(f"{BASE_URL}/synthesize", params=params)
        
        if response.status_code == 200:
            output_path = Path(f"test_rate_{rate}.wav")
            with open(output_path, "wb") as f:
                f.write(response.content)
            print(f"✓ Rate {rate} - Audio saved to: {output_path}")
        else:
            print(f"✗ Rate {rate} failed with status {response.status_code}")
    
    print("✓ Different speech rates test completed\n")

def test_error_cases():
    """Test error handling."""
    print("Testing error cases...")
    
    # Test empty text
    response = requests.post(f"{BASE_URL}/synthesize", json={"text": ""})
    print(f"Empty text status: {response.status_code}")
    assert response.status_code == 422  # Validation error
    
    # Test invalid speaker ID
    response = requests.post(f"{BASE_URL}/synthesize", json={"text": "test", "speaker_id": 999})
    print(f"Invalid speaker status: {response.status_code}")
    assert response.status_code == 400  # Bad request
    
    # Test invalid speech rate
    response = requests.post(f"{BASE_URL}/synthesize", json={"text": "test", "speech_rate": 0})
    print(f"Invalid speech rate status: {response.status_code}")
    assert response.status_code == 422  # Validation error
    
    print("✓ Error cases test passed\n")

def main():
    """Run all tests."""
    print("Starting API tests...\n")
    
    try:
        test_health_check()
        test_model_info()
        test_speakers()
        test_synthesis_post()
        test_synthesis_get()
        test_different_speakers()
        test_speech_rates()
        test_error_cases()
        
        print("🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
