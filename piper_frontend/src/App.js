import React, { useState, useEffect } from 'react';

function App() {
  const [text, setText] = useState('नमस्ते, यो एक परीक्षण हो।');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.0);
  const [isLoading, setIsLoading] = useState(false);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [apiStatus, setApiStatus] = useState('checking');
  const [audioUrl, setAudioUrl] = useState(null);

  const API_BASE = 'http://localhost:8000';

  // Check API status and load voice info
  useEffect(() => {
    checkApiStatus();
    loadVoiceInfo();
    loadSpeakers();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/`);
      const data = await response.json();
      setApiStatus(data.voice_loaded ? 'ready' : 'no-voice');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadVoiceInfo = async () => {
    try {
      const response = await fetch(`${API_BASE}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        setVoiceInfo(data);
      }
    } catch (error) {
      console.error('Failed to load voice info:', error);
    }
  };

  const loadSpeakers = async () => {
    try {
      const response = await fetch(`${API_BASE}/speakers`);
      if (response.ok) {
        const data = await response.json();
        setSpeakers(data.speakers || []);
      }
    } catch (error) {
      console.error('Failed to load speakers:', error);
    }
  };

  const synthesizeSpeech = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    setAudioUrl(null);

    try {
      const params = new URLSearchParams({
        text: text,
        speaker_id: speakerId,
        speech_rate: speechRate,
        noise_scale: noiseScale,
        noise_w: noiseW,
        sentence_silence: sentenceSilence
      });

      const response = await fetch(`${API_BASE}/synthesize?${params}`);

      if (response.ok) {
        const audioBlob = await response.blob();
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
      } else {
        alert('Failed to synthesize speech');
      }
    } catch (error) {
      console.error('Synthesis error:', error);
      alert('Error connecting to API');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = () => {
    switch (apiStatus) {
      case 'ready': return 'text-green-600';
      case 'no-voice': return 'text-yellow-600';
      case 'offline': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'ready': return 'API Ready';
      case 'no-voice': return 'No Voice Loaded';
      case 'offline': return 'API Offline';
      default: return 'Checking...';
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Piper TTS Dashboard</h1>
          <div className="flex items-center space-x-4">
            <span className={`font-semibold ${getStatusColor()}`}>
              ● {getStatusText()}
            </span>
            {voiceInfo && (
              <span className="text-gray-600">
                {voiceInfo.num_speakers} speakers • {voiceInfo.sample_rate}Hz • {voiceInfo.espeak_voice}
              </span>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Panel */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Text Input</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Text to Synthesize
                </label>
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="4"
                  placeholder="Enter Nepali text here..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Speaker ID ({speakers.length} available)
                  </label>
                  <select
                    value={speakerId}
                    onChange={(e) => setSpeakerId(parseInt(e.target.value))}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  >
                    {speakers.map((speaker) => (
                      <option key={speaker.speaker_id} value={speaker.speaker_id}>
                        Speaker {speaker.speaker_id}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Speech Rate: {speechRate}x
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    value={speechRate}
                    onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Controls Panel */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Advanced Controls</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Noise Scale: {noiseScale}
                </label>
                <input
                  type="range"
                  min="0.0"
                  max="1.0"
                  step="0.01"
                  value={noiseScale}
                  onChange={(e) => setNoiseScale(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phoneme Width Noise: {noiseW}
                </label>
                <input
                  type="range"
                  min="0.0"
                  max="1.0"
                  step="0.01"
                  value={noiseW}
                  onChange={(e) => setNoiseW(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sentence Silence: {sentenceSilence}s
                </label>
                <input
                  type="range"
                  min="0.0"
                  max="2.0"
                  step="0.1"
                  value={sentenceSilence}
                  onChange={(e) => setSentenceSilence(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              <button
                onClick={synthesizeSpeech}
                disabled={isLoading || apiStatus !== 'ready' || !text.trim()}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-semibold"
              >
                {isLoading ? 'Synthesizing...' : 'Generate Speech'}
              </button>
            </div>
          </div>
        </div>

        {/* Audio Output */}
        {audioUrl && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Generated Audio</h2>
            <audio controls className="w-full" src={audioUrl}>
              Your browser does not support the audio element.
            </audio>
            <div className="mt-4">
              <a
                href={audioUrl}
                download="piper_tts_output.wav"
                className="inline-block bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
              >
                Download Audio
              </a>
            </div>
          </div>
        )}

        {/* Quick Test Buttons */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Quick Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => setText('नमस्ते, म पाइपर हुँ।')}
              className="p-3 bg-gray-100 hover:bg-gray-200 rounded-md text-left"
            >
              <div className="font-medium">Greeting</div>
              <div className="text-sm text-gray-600">नमस्ते, म पाइपर हुँ।</div>
            </button>
            <button
              onClick={() => setText('आज मौसम राम्रो छ।')}
              className="p-3 bg-gray-100 hover:bg-gray-200 rounded-md text-left"
            >
              <div className="font-medium">Weather</div>
              <div className="text-sm text-gray-600">आज मौसम राम्रो छ।</div>
            </button>
            <button
              onClick={() => setText('धन्यवाद र नमस्कार।')}
              className="p-3 bg-gray-100 hover:bg-gray-200 rounded-md text-left"
            >
              <div className="font-medium">Thanks</div>
              <div className="text-sm text-gray-600">धन्यवाद र नमस्कार।</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
