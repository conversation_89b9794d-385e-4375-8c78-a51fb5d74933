# [postcss][postcss]-merge-longhand

> Merge longhand properties into shorthand with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-merge-longhand) do:

```
npm install postcss-merge-longhand --save
```

## Example

Merge longhand properties into shorthand; works with `margin`, `padding` &
`border`. For more examples see the [tests](src/__tests__/index.js).

### Input

```css
h1 {
    margin-top: 10px;
    margin-right: 20px;
    margin-bottom: 10px;
    margin-left: 20px;
}
```

### Output

```css
h1 {
    margin: 10px 20px;
}
```

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
