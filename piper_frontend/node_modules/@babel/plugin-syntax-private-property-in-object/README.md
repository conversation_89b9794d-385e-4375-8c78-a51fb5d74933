# @babel/plugin-syntax-private-property-in-object

> Allow parsing of '#foo in obj' brand checks

See our website [@babel/plugin-syntax-private-property-in-object](https://babeljs.io/docs/en/babel-plugin-syntax-private-property-in-object) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-private-property-in-object
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-private-property-in-object --dev
```
