{"name": "postcss-normalize-display-values", "version": "5.1.0", "description": "Normalize multiple value display syntaxes into single values.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-display-values\n\n> Normalize display property values with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-display-values) do:\n\n```\nnpm install postcss-normalize-display-values --save\n```\n\n## Example\n\n### Input\n\n```css\ndiv {\n    display: inline flow-root\n}\n```\n\n### Output\n\n```css\ndiv {\n    display: inline-block\n}\n``` \n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}