/// <reference types="node" />
declare const g: typeof globalThis | NodeJS.Global;
declare class AssertionError extends g.Error {
    generatedMessage: any;
    name: any;
    code: any;
    actual: any;
    expected: any;
    operator: any;
    constructor(options: any);
}
declare function message(key: any, args: any): any;
declare function E(sym: any, val: any): void;
export declare const Error: {
    new (key: any, ...args: any[]): {
        [x: string]: any;
    };
    [x: string]: any;
};
export declare const TypeError: {
    new (key: any, ...args: any[]): {
        [x: string]: any;
    };
    [x: string]: any;
};
export declare const RangeError: {
    new (key: any, ...args: any[]): {
        [x: string]: any;
    };
    [x: string]: any;
};
export { message, AssertionError, E, };
