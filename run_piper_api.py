#!/usr/bin/env python3
"""
Startup script for the Piper TTS API using official implementation.
"""

import argparse
import uvicorn
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Start Piper TTS FastAPI server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model", help="Path to ONNX model file")
    parser.add_argument("--config", help="Path to model config file")
    parser.add_argument("--cuda", action="store_true", help="Use CUDA acceleration")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    
    args = parser.parse_args()
    
    # Set environment variables for model loading
    if args.model:
        import os
        os.environ["PIPER_MODEL_PATH"] = args.model
        if args.config:
            os.environ["PIPER_CONFIG_PATH"] = args.config
        if args.cuda:
            os.environ["PIPER_USE_CUDA"] = "true"
    
    print(f"Starting Piper TTS API server on {args.host}:{args.port}")
    if args.model:
        print(f"Model: {args.model}")
        print(f"Config: {args.config or f'{args.model}.json'}")
        print(f"CUDA: {args.cuda}")
    
    uvicorn.run(
        "piper_api.fastapi_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
