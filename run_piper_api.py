#!/usr/bin/env python3
"""
Simple script to run the Piper TTS API server.
"""

import uvicorn
from piper_api.fastapi_server import app

if __name__ == "__main__":
    print("Starting Piper TTS API server...")
    print("API will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
