#!/usr/bin/env python3
"""
FastAPI server for Piper TTS using the official Piper implementation.
"""

import io
import logging
import wave
from pathlib import Path
from typing import Optional, Dict, Any, List

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import Response
from pydantic import BaseModel, Field

from .voice import PiperVoice

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Piper TTS API",
    description="Text-to-Speech API using official Piper implementation with speaker and speech rate control",
    version="2.0.0"
)

# Global voice instance
voice: Optional[PiperVoice] = None
voice_config = {}

# Pydantic models for request/response
class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=2000)
    speaker_id: Optional[int] = Field(None, description="Speaker ID", ge=0)
    speech_rate: Optional[float] = Field(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0)
    noise_scale: Optional[float] = Field(None, description="Generator noise (0.0-1.0)")
    noise_w: Optional[float] = Field(None, description="Phoneme width noise (0.0-1.0)")
    sentence_silence: Optional[float] = Field(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)

class VoiceInfo(BaseModel):
    num_speakers: int
    sample_rate: int
    espeak_voice: str
    phoneme_type: str
    length_scale: float
    noise_scale: float
    noise_w: float

class SpeakerInfo(BaseModel):
    speaker_id: int
    total_speakers: int

def load_voice(model_path: str, config_path: Optional[str] = None, use_cuda: bool = False):
    """Load a Piper voice model."""
    global voice, voice_config
    
    try:
        voice = PiperVoice.load(model_path, config_path=config_path, use_cuda=use_cuda)
        voice_config = {
            "model_path": model_path,
            "config_path": config_path or f"{model_path}.json",
            "use_cuda": use_cuda
        }
        logger.info(f"Voice loaded successfully from {model_path}")
        logger.info(f"Voice config: speakers={voice.config.num_speakers}, sample_rate={voice.config.sample_rate}")
        return True
    except Exception as e:
        logger.error(f"Failed to load voice: {e}")
        return False

@app.on_event("startup")
async def startup_event():
    """Initialize the voice on startup."""
    # Try to load a default model if available
    model_paths = [
        "models/ne_NP-google-medium.onnx",
        "ne_NP-google-medium.onnx",
        "voice.onnx"
    ]
    
    for model_path in model_paths:
        if Path(model_path).exists():
            if load_voice(model_path):
                break
    else:
        logger.warning("No voice model found. Use /load_voice endpoint to load a model.")

@app.get("/", summary="Health check")
async def root():
    """Health check endpoint."""
    return {
        "message": "Piper TTS API is running",
        "status": "healthy",
        "voice_loaded": voice is not None
    }

@app.post("/load_voice", summary="Load a voice model")
async def load_voice_endpoint(
    model_path: str = Query(..., description="Path to ONNX model file"),
    config_path: Optional[str] = Query(None, description="Path to config JSON file"),
    use_cuda: bool = Query(False, description="Use CUDA acceleration")
):
    """Load a voice model from the specified path."""
    if not Path(model_path).exists():
        raise HTTPException(status_code=404, detail=f"Model file not found: {model_path}")
    
    if config_path and not Path(config_path).exists():
        raise HTTPException(status_code=404, detail=f"Config file not found: {config_path}")
    
    success = load_voice(model_path, config_path, use_cuda)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to load voice model")
    
    return {
        "message": "Voice loaded successfully",
        "model_path": model_path,
        "config_path": config_path or f"{model_path}.json",
        "voice_info": await get_voice_info()
    }

@app.get("/voice/info", response_model=VoiceInfo, summary="Get voice information")
async def get_voice_info():
    """Get information about the loaded voice."""
    if voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")
    
    return VoiceInfo(
        num_speakers=voice.config.num_speakers,
        sample_rate=voice.config.sample_rate,
        espeak_voice=voice.config.espeak_voice,
        phoneme_type=voice.config.phoneme_type.value,
        length_scale=voice.config.length_scale,
        noise_scale=voice.config.noise_scale,
        noise_w=voice.config.noise_w
    )

@app.get("/speakers", summary="Get speaker information")
async def get_speakers():
    """Get information about available speakers."""
    if voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")
    
    return {
        "num_speakers": voice.config.num_speakers,
        "speakers": [{"speaker_id": i} for i in range(voice.config.num_speakers)]
    }

@app.post("/synthesize", summary="Synthesize speech from text")
async def synthesize_speech(request: TTSRequest):
    """
    Synthesize speech from text with speaker and speech rate control.
    
    Returns WAV audio data.
    """
    if voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")
    
    try:
        # Validate speaker ID
        if request.speaker_id is not None:
            if request.speaker_id < 0 or request.speaker_id >= voice.config.num_speakers:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Speaker ID must be between 0 and {voice.config.num_speakers - 1}"
                )
        
        # Convert speech_rate to length_scale (inverse relationship)
        length_scale = 1.0 / request.speech_rate if request.speech_rate else None
        
        # Generate audio using official Piper implementation
        with io.BytesIO() as wav_io:
            with wave.open(wav_io, "wb") as wav_file:
                voice.synthesize(
                    text=request.text,
                    wav_file=wav_file,
                    speaker_id=request.speaker_id,
                    length_scale=length_scale,
                    noise_scale=request.noise_scale,
                    noise_w=request.noise_w,
                    sentence_silence=request.sentence_silence or 0.0
                )
            
            wav_data = wav_io.getvalue()
        
        # Return WAV file
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "Content-Length": str(len(wav_data))
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error during synthesis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during synthesis")

@app.get("/synthesize", summary="Synthesize speech from text (GET)")
async def synthesize_speech_get(
    text: str = Query(..., description="Text to synthesize", min_length=1, max_length=2000),
    speaker_id: Optional[int] = Query(None, description="Speaker ID", ge=0),
    speech_rate: Optional[float] = Query(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0),
    noise_scale: Optional[float] = Query(None, description="Generator noise (0.0-1.0)", ge=0.0, le=1.0),
    noise_w: Optional[float] = Query(None, description="Phoneme width noise (0.0-1.0)", ge=0.0, le=1.0),
    sentence_silence: Optional[float] = Query(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)
):
    """
    Synthesize speech from text using GET parameters.
    
    Returns WAV audio data.
    """
    request = TTSRequest(
        text=text,
        speaker_id=speaker_id,
        speech_rate=speech_rate,
        noise_scale=noise_scale,
        noise_w=noise_w,
        sentence_silence=sentence_silence
    )
    return await synthesize_speech(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
