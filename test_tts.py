#!/usr/bin/env python3
"""
Test script for the Piper TTS engine.
"""

from pathlib import Path
from api.piper_tts import PiperTTS

def test_tts():
    """Test the TTS engine functionality."""
    try:
        # Initialize TTS engine
        model_path = Path("models/ne_NP-google-medium.onnx")
        config_path = Path("models/ne_NP-google-medium.onnx.json")
        
        print("Initializing TTS engine...")
        tts = PiperTTS(model_path, config_path)
        
        # Get model info
        info = tts.get_model_info()
        print(f"Model info: {info}")
        
        # Get available speakers
        speakers = tts.get_available_speakers()
        print(f"Available speakers: {speakers}")
        
        # Test synthesis
        test_text = "नमस्ते, यो एक परीक्षण हो।"  # "Hello, this is a test." in Nepali
        print(f"Synthesizing text: {test_text}")
        
        # Test with different parameters
        wav_data = tts.synthesize_to_wav(
            text=test_text,
            speaker_id=0,
            length_scale=1.0,  # Normal speed
            noise_scale=0.667  # Default variability
        )
        
        # Save test output
        output_path = Path("test_output.wav")
        with open(output_path, "wb") as f:
            f.write(wav_data)
        
        print(f"Audio saved to: {output_path}")
        print(f"Audio length: {len(wav_data)} bytes")
        print("TTS test completed successfully!")
        
    except Exception as e:
        print(f"Error during TTS test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tts()
