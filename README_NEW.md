# Piper TTS API v2.0

A modern FastAPI-based Text-to-Speech service using the **official Piper implementation** with speaker and speech rate control.

## 🎯 Features

- ✅ **Official Piper Implementation**: Uses the authentic Piper TTS engine
- ✅ **Multi-speaker Support**: 18 different Nepali speakers
- ✅ **Speech Rate Control**: Adjust speaking speed from 0.1x to 3.0x
- ✅ **Advanced Parameters**: Fine-tune voice variability and noise
- ✅ **REST API**: Both GET and POST endpoints
- ✅ **High-Quality Audio**: 22.05kHz, 16-bit WAV output
- ✅ **Real-time Synthesis**: Fast ONNX-based inference
- ✅ **Interactive Documentation**: Built-in Swagger UI

## 📋 Model Information

- **Language**: Nepali (नेपाली)
- **Model**: Google medium quality (73MB)
- **Speakers**: 18 different voices (IDs 0-17)
- **Sample Rate**: 22,050 Hz
- **Format**: WAV (16-bit PCM, mono)
- **Phoneme Type**: eSpeak

## 🚀 Quick Start

### Prerequisites

- Python 3.11
- uv package manager

### Installation

1. **Initialize project**:
   ```bash
   uv init --python=3.11 .
   ```

2. **Install dependencies**:
   ```bash
   uv add "piper-phonemize~=1.1.0" "onnxruntime>=1.11.0,<2" "flask>=3,<4" "fastapi[standard]" requests
   ```

3. **Download model** (already included):
   ```bash
   mkdir models
   cd models
   wget https://huggingface.co/rhasspy/piper-voices/resolve/main/ne/ne_NP/google/medium/ne_NP-google-medium.onnx
   wget https://huggingface.co/rhasspy/piper-voices/resolve/main/ne/ne_NP/google/medium/ne_NP-google-medium.onnx.json
   ```

### Start the Server

```bash
# Activate virtual environment
source .venv/bin/activate

# Start server with model
python run_piper_api.py --model models/ne_NP-google-medium.onnx
```

Server will be available at: **http://localhost:8000**

## 📖 API Documentation

### Interactive Docs
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Endpoints

#### 🏥 Health Check
```bash
GET /
```

#### ℹ️ Voice Information
```bash
GET /voice/info
```

#### 👥 Available Speakers
```bash
GET /speakers
```

#### 🎤 Text-to-Speech Synthesis

**GET Request**:
```bash
curl "http://localhost:8000/synthesize?text=नमस्ते&speaker_id=0&speech_rate=1.0" \
     --output speech.wav
```

**POST Request**:
```bash
curl -X POST "http://localhost:8000/synthesize" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "नमस्ते, यो एक परीक्षण हो।",
       "speaker_id": 5,
       "speech_rate": 1.2,
       "noise_scale": 0.5,
       "noise_w": 0.7,
       "sentence_silence": 0.3
     }' \
     --output speech.wav
```

### 🎛️ Parameters

| Parameter | Type | Range | Default | Description |
|-----------|------|-------|---------|-------------|
| `text` | string | 1-2000 chars | required | Text to synthesize |
| `speaker_id` | integer | 0-17 | 0 | Speaker voice ID |
| `speech_rate` | float | 0.1-3.0 | 1.0 | Speech speed (0.5=slower, 2.0=faster) |
| `noise_scale` | float | 0.0-1.0 | 0.667 | Generator noise (voice variability) |
| `noise_w` | float | 0.0-1.0 | 0.8 | Phoneme width noise |
| `sentence_silence` | float | 0.0-2.0 | 0.0 | Silence after sentences (seconds) |

## 🎭 Available Speakers

The model includes **18 different speakers** with unique voice characteristics:
- **Speaker 0-17**: Each with distinct tone, pitch, and speaking style
- Use `/speakers` endpoint to get the complete list

## 🧪 Testing & Examples

### Run Direct Tests
```bash
python test_piper_api.py
```

### Run Comprehensive Examples
```bash
python example_piper_usage.py
```

### Python Usage Example

```python
import requests

# Basic synthesis
response = requests.get("http://localhost:8000/synthesize", params={
    "text": "नमस्ते, कस्तो छ?",
    "speaker_id": 5,
    "speech_rate": 1.2
})

# Save audio file
with open("output.wav", "wb") as f:
    f.write(response.content)

# Advanced synthesis with POST
response = requests.post("http://localhost:8000/synthesize", json={
    "text": "यो उन्नत संश्लेषण हो।",
    "speaker_id": 10,
    "speech_rate": 0.9,
    "noise_scale": 0.4,
    "noise_w": 0.6,
    "sentence_silence": 0.5
})
```

## 📁 Project Structure

```
.
├── piper_api/                    # Official Piper implementation
│   ├── fastapi_server.py        # FastAPI wrapper
│   ├── voice.py                 # Piper voice engine
│   ├── config.py                # Configuration classes
│   └── ...                      # Other Piper modules
├── models/                      # Model files
│   ├── ne_NP-google-medium.onnx      # ONNX model (73MB)
│   └── ne_NP-google-medium.onnx.json # Model configuration
├── run_piper_api.py             # Server startup script
├── test_piper_api.py            # Comprehensive tests
├── example_piper_usage.py       # Usage examples
├── pyproject.toml               # Project dependencies
└── README_NEW.md               # This file
```

## ⚡ Performance

- **Model size**: 73MB
- **Inference speed**: ~0.5-2 seconds per sentence
- **Memory usage**: ~150-200MB RAM
- **Audio quality**: 22.05kHz, 16-bit WAV
- **Concurrent requests**: Supported

## 🔧 Advanced Usage

### Load Different Models
```bash
python run_piper_api.py --model path/to/your/model.onnx --config path/to/config.json
```

### Enable CUDA (if available)
```bash
python run_piper_api.py --model models/ne_NP-google-medium.onnx --cuda
```

### Custom Port
```bash
python run_piper_api.py --model models/ne_NP-google-medium.onnx --port 9000
```

## 🐛 Troubleshooting

### Common Issues

1. **piper-phonemize installation fails**:
   - Use Python 3.11 (required for compatibility)
   - Install system dependencies: `sudo apt-get install espeak-ng`

2. **Model files not found**:
   - Ensure model files are in the `models/` directory
   - Check file permissions and paths

3. **Server won't start**:
   - Check if port is available: `lsof -i :8000`
   - Verify virtual environment is activated

4. **Audio quality issues**:
   - Try different speakers (0-17)
   - Adjust `noise_scale` and `noise_w` parameters
   - Use appropriate `speech_rate` values

### Debug Mode
```bash
python run_piper_api.py --model models/ne_NP-google-medium.onnx --reload
```

## 🎉 What's New in v2.0

- ✅ **Official Piper Implementation**: No more custom phonemization
- ✅ **Better Audio Quality**: Proper sentence boundary handling
- ✅ **More Parameters**: Advanced noise and timing controls
- ✅ **Improved Error Handling**: Better validation and messages
- ✅ **Enhanced Documentation**: Comprehensive examples and guides
- ✅ **Model Loading**: Dynamic model loading via API

## 📄 License

MIT License - see the Piper project for model licensing details.

## 🙏 Credits

- **Piper TTS**: https://github.com/rhasspy/piper (Official implementation)
- **Model**: Google Nepali medium quality model from Hugging Face
- **FastAPI**: https://fastapi.tiangolo.com/
- **ONNX Runtime**: https://onnxruntime.ai/
