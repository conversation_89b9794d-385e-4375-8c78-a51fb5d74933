#!/usr/bin/env python3
"""
Example usage of the Piper TTS API.
"""

import requests
import urllib.parse
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_basic_synthesis():
    """Test basic text-to-speech synthesis."""
    print("=== Basic TTS Test ===")
    
    # Simple English text
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": "Hello, this is a test.",
        "speaker_id": 0,
        "speech_rate": 1.0
    })
    
    if response.status_code == 200:
        with open("example_english.wav", "wb") as f:
            f.write(response.content)
        print("✓ English synthesis saved to example_english.wav")
    else:
        print(f"✗ Error: {response.status_code}")

def test_nepali_synthesis():
    """Test Nepali text synthesis."""
    print("\n=== Nepali TTS Test ===")
    
    # Nepali text
    nepali_text = "नमस्ते, यो एक परीक्षण हो।"
    
    response = requests.get(f"{BASE_URL}/synthesize", params={
        "text": nepali_text,
        "speaker_id": 0,
        "speech_rate": 1.0
    })
    
    if response.status_code == 200:
        with open("example_nepali.wav", "wb") as f:
            f.write(response.content)
        print("✓ Nepali synthesis saved to example_nepali.wav")
    else:
        print(f"✗ Error: {response.status_code}")

def test_different_speakers():
    """Test different speakers."""
    print("\n=== Different Speakers Test ===")
    
    text = "नमस्ते"
    speakers = [0, 5, 10, 15]
    
    for speaker_id in speakers:
        response = requests.get(f"{BASE_URL}/synthesize", params={
            "text": text,
            "speaker_id": speaker_id,
            "speech_rate": 1.0
        })
        
        if response.status_code == 200:
            filename = f"example_speaker_{speaker_id}.wav"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"✓ Speaker {speaker_id} saved to {filename}")
        else:
            print(f"✗ Speaker {speaker_id} failed: {response.status_code}")

def test_speech_rates():
    """Test different speech rates."""
    print("\n=== Speech Rate Test ===")
    
    text = "गति परीक्षण"
    rates = [0.5, 1.0, 1.5, 2.0]
    
    for rate in rates:
        response = requests.get(f"{BASE_URL}/synthesize", params={
            "text": text,
            "speaker_id": 0,
            "speech_rate": rate
        })
        
        if response.status_code == 200:
            filename = f"example_rate_{rate}.wav"
            with open(filename, "wb") as f:
                f.write(response.content)
            print(f"✓ Rate {rate}x saved to {filename}")
        else:
            print(f"✗ Rate {rate}x failed: {response.status_code}")

def test_post_request():
    """Test POST request with JSON."""
    print("\n=== POST Request Test ===")
    
    data = {
        "text": "यो POST अनुरोध परीक्षण हो।",
        "speaker_id": 3,
        "speech_rate": 1.2,
        "variability": 0.5,
        "noise_w": 0.7
    }
    
    response = requests.post(f"{BASE_URL}/synthesize", json=data)
    
    if response.status_code == 200:
        with open("example_post.wav", "wb") as f:
            f.write(response.content)
        print("✓ POST request saved to example_post.wav")
    else:
        print(f"✗ POST request failed: {response.status_code}")

def get_model_info():
    """Get and display model information."""
    print("\n=== Model Information ===")
    
    response = requests.get(f"{BASE_URL}/model/info")
    if response.status_code == 200:
        info = response.json()
        print(f"Language: {info['language']['name_english']} ({info['language']['name_native']})")
        print(f"Sample Rate: {info['sample_rate']} Hz")
        print(f"Number of Speakers: {info['num_speakers']}")
        print(f"Available Speakers: {', '.join(info['available_speakers'])}")
    else:
        print(f"✗ Failed to get model info: {response.status_code}")

def get_speakers():
    """Get and display speaker information."""
    print("\n=== Speaker Information ===")
    
    response = requests.get(f"{BASE_URL}/speakers")
    if response.status_code == 200:
        data = response.json()
        print(f"Total speakers: {data['num_speakers']}")
        for speaker in data['speakers']:
            print(f"  Speaker {speaker['speaker_id']}: {speaker['speaker_name']}")
    else:
        print(f"✗ Failed to get speakers: {response.status_code}")

def main():
    """Run all examples."""
    print("Piper TTS API Usage Examples")
    print("=" * 40)
    
    try:
        # Check if server is running
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("❌ Server is not running. Please start it with: python run_api.py")
            return
        
        get_model_info()
        get_speakers()
        test_basic_synthesis()
        test_nepali_synthesis()
        test_different_speakers()
        test_speech_rates()
        test_post_request()
        
        print("\n🎉 All examples completed successfully!")
        print("\nGenerated files:")
        for file in Path(".").glob("example_*.wav"):
            print(f"  - {file}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start it with: python run_api.py")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
