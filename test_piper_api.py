#!/usr/bin/env python3
"""
Test script for the new Piper TTS API using official implementation.
"""

import sys
import io
import wave
from pathlib import Path

# Add piper_api to path
sys.path.insert(0, str(Path(__file__).parent))

from piper_api.voice import <PERSON><PERSON><PERSON><PERSON>

def test_direct_piper():
    """Test the Piper voice directly."""
    print("=== Testing Direct Piper Implementation ===")
    
    model_path = "models/ne_NP-google-medium.onnx"
    config_path = "models/ne_NP-google-medium.onnx.json"
    
    if not Path(model_path).exists():
        print(f"❌ Model file not found: {model_path}")
        return False
    
    if not Path(config_path).exists():
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Load voice
        print("Loading voice...")
        voice = PiperVoice.load(model_path, config_path)
        print(f"✓ Voice loaded successfully")
        print(f"  - Speakers: {voice.config.num_speakers}")
        print(f"  - Sample rate: {voice.config.sample_rate}")
        print(f"  - Espeak voice: {voice.config.espeak_voice}")
        
        # Test phonemization
        test_text = "नमस्ते, यो एक परीक्षण हो।"
        print(f"\nTesting phonemization with text: '{test_text}'")
        phonemes = voice.phonemize(test_text)
        print(f"✓ Phonemes: {phonemes}")
        
        # Test synthesis
        print("\nTesting synthesis...")
        with io.BytesIO() as wav_io:
            with wave.open(wav_io, "wb") as wav_file:
                voice.synthesize(
                    text=test_text,
                    wav_file=wav_file,
                    speaker_id=0,
                    length_scale=1.0,
                    noise_scale=0.667,
                    noise_w=0.8
                )
            
            wav_data = wav_io.getvalue()
        
        # Save test output
        output_path = Path("test_direct_piper.wav")
        with open(output_path, "wb") as f:
            f.write(wav_data)
        
        print(f"✓ Audio generated: {len(wav_data)} bytes")
        print(f"✓ Audio saved to: {output_path}")
        
        # Test different speakers
        print("\nTesting different speakers...")
        for speaker_id in [0, 5, 10, 15]:
            if speaker_id < voice.config.num_speakers:
                with io.BytesIO() as wav_io:
                    with wave.open(wav_io, "wb") as wav_file:
                        voice.synthesize(
                            text="नमस्ते",
                            wav_file=wav_file,
                            speaker_id=speaker_id
                        )
                    
                    wav_data = wav_io.getvalue()
                    output_path = Path(f"test_speaker_{speaker_id}.wav")
                    with open(output_path, "wb") as f:
                        f.write(wav_data)
                    
                    print(f"✓ Speaker {speaker_id}: {len(wav_data)} bytes -> {output_path}")
        
        # Test different speech rates
        print("\nTesting different speech rates...")
        rates = [0.5, 1.0, 1.5, 2.0]  # length_scale values
        for rate in rates:
            with io.BytesIO() as wav_io:
                with wave.open(wav_io, "wb") as wav_file:
                    voice.synthesize(
                        text="गति परीक्षण",
                        wav_file=wav_file,
                        speaker_id=0,
                        length_scale=rate
                    )
                
                wav_data = wav_io.getvalue()
                output_path = Path(f"test_rate_{rate}.wav")
                with open(output_path, "wb") as f:
                    f.write(wav_data)
                
                print(f"✓ Rate {rate}: {len(wav_data)} bytes -> {output_path}")
        
        print("\n🎉 Direct Piper test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_server():
    """Test the FastAPI server."""
    print("\n=== Testing FastAPI Server ===")
    
    try:
        import requests
        import time
        import subprocess
        import signal
        import os
        
        # Start the server
        print("Starting API server...")
        server_process = subprocess.Popen([
            sys.executable, "run_piper_api.py",
            "--model", "models/ne_NP-google-medium.onnx",
            "--port", "8001"
        ])
        
        # Wait for server to start
        time.sleep(5)
        
        base_url = "http://localhost:8001"
        
        # Test health check
        print("Testing health check...")
        response = requests.get(f"{base_url}/")
        print(f"✓ Health check: {response.status_code} - {response.json()}")
        
        # Test voice info
        print("Testing voice info...")
        response = requests.get(f"{base_url}/voice/info")
        if response.status_code == 200:
            info = response.json()
            print(f"✓ Voice info: {info}")
        else:
            print(f"❌ Voice info failed: {response.status_code}")
        
        # Test speakers
        print("Testing speakers...")
        response = requests.get(f"{base_url}/speakers")
        if response.status_code == 200:
            speakers = response.json()
            print(f"✓ Speakers: {speakers}")
        else:
            print(f"❌ Speakers failed: {response.status_code}")
        
        # Test synthesis
        print("Testing synthesis...")
        response = requests.get(f"{base_url}/synthesize", params={
            "text": "नमस्ते",
            "speaker_id": 0,
            "speech_rate": 1.0
        })
        
        if response.status_code == 200:
            output_path = Path("test_api_synthesis.wav")
            with open(output_path, "wb") as f:
                f.write(response.content)
            print(f"✓ Synthesis: {len(response.content)} bytes -> {output_path}")
        else:
            print(f"❌ Synthesis failed: {response.status_code}")
        
        # Test POST synthesis
        print("Testing POST synthesis...")
        response = requests.post(f"{base_url}/synthesize", json={
            "text": "यो POST अनुरोध हो।",
            "speaker_id": 5,
            "speech_rate": 1.2
        })
        
        if response.status_code == 200:
            output_path = Path("test_api_post.wav")
            with open(output_path, "wb") as f:
                f.write(response.content)
            print(f"✓ POST synthesis: {len(response.content)} bytes -> {output_path}")
        else:
            print(f"❌ POST synthesis failed: {response.status_code}")
        
        print("\n🎉 API server test completed successfully!")
        
        # Stop the server
        server_process.terminate()
        server_process.wait()
        
        return True
        
    except Exception as e:
        print(f"❌ API server test failed: {e}")
        if 'server_process' in locals():
            server_process.terminate()
        return False

def main():
    """Run all tests."""
    print("Piper TTS API Tests")
    print("=" * 50)
    
    # Test direct Piper implementation
    direct_success = test_direct_piper()
    
    # Test API server (optional, requires requests)
    try:
        import requests
        api_success = test_api_server()
    except ImportError:
        print("\n⚠️  Skipping API server test (requests not installed)")
        api_success = True
    
    print("\n" + "=" * 50)
    if direct_success:
        print("✅ All tests passed!")
        print("\nGenerated test files:")
        for wav_file in Path(".").glob("test_*.wav"):
            print(f"  - {wav_file}")
    else:
        print("❌ Some tests failed!")

if __name__ == "__main__":
    main()
