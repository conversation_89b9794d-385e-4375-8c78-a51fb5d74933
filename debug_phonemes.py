#!/usr/bin/env python3
"""
Debug script to understand phonemization issues.
"""

from piper_phonemize import phonemize_espeak
import json
from pathlib import Path

def debug_phonemes():
    """Debug the phonemization process."""
    
    # Load config to understand phoneme mapping
    config_path = Path("models/ne_NP-google-medium.onnx.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("=== Model Configuration ===")
    print(f"Language: {config['espeak']['voice']}")
    print(f"Phoneme type: {config['phoneme_type']}")
    print(f"Number of symbols: {config['num_symbols']}")
    print()
    
    # Test phonemization
    test_texts = [
        "Hello world",  # English test
        "नमस्ते",        # Simple Nepali greeting
        "नमस्ते, यो एक परीक्षण हो।",  # Full Nepali sentence
    ]
    
    for text in test_texts:
        print(f"=== Testing text: '{text}' ===")
        
        # Try phonemization with different language codes
        language_codes = ['ne', 'ne-np', config['espeak']['voice']]
        
        for lang_code in language_codes:
            try:
                print(f"Language code: {lang_code}")
                phonemes = phonemize_espeak(text, lang_code)
                print(f"Phonemes result: {phonemes}")
                
                if phonemes:
                    phoneme_str = phonemes[0] if isinstance(phonemes, list) else phonemes
                    print(f"Phoneme string: '{phoneme_str}'")
                    print(f"Phoneme characters: {list(phoneme_str)}")
                    
                    # Check which phonemes are in the model's phoneme map
                    phoneme_id_map = config['phoneme_id_map']
                    mapped_phonemes = []
                    unmapped_phonemes = []
                    
                    for phoneme in phoneme_str:
                        if phoneme in phoneme_id_map:
                            mapped_phonemes.append((phoneme, phoneme_id_map[phoneme]))
                        else:
                            unmapped_phonemes.append(phoneme)
                    
                    print(f"Mapped phonemes: {mapped_phonemes}")
                    print(f"Unmapped phonemes: {unmapped_phonemes}")
                    print()
                    
                    if mapped_phonemes:
                        break  # Found working language code
                        
            except Exception as e:
                print(f"Error with {lang_code}: {e}")
                print()
    
    # Show available phonemes in model
    print("=== Available phonemes in model ===")
    phoneme_id_map = config['phoneme_id_map']
    print(f"Total phonemes: {len(phoneme_id_map)}")
    
    # Group by type
    letters = []
    symbols = []
    ipa = []
    
    for phoneme in phoneme_id_map.keys():
        if phoneme.isalpha():
            letters.append(phoneme)
        elif len(phoneme) == 1 and ord(phoneme) < 128:
            symbols.append(phoneme)
        else:
            ipa.append(phoneme)
    
    print(f"Letters: {sorted(letters)}")
    print(f"Symbols: {sorted(symbols)}")
    print(f"IPA phonemes: {sorted(ipa)}")

if __name__ == "__main__":
    debug_phonemes()
